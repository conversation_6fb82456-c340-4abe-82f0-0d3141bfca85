/*
 * DatabaseORA.h
 *
 *  Created on: 2009. 8. 28.
 *      Author: Administrator
 */

#ifndef DATABASEORA_H_
#define DATABASEORA_H_

#include <string>
#include <vector>
#include "PacketCtrlSKY.h"
using namespace std;

// ahn 2013.11.14
//#define DEF_TELCO_SKY 4

namespace KSKYB
{

class CDatabaseORA
{
public:
	CDatabaseORA() {};
	virtual ~CDatabaseORA() {};

	int setEnableThreads();
	int initThread(sql_context& ctx);
	int freeThread(sql_context& ctx);
	int connectToOracle(sql_context ctx, char*, char*);
	int closeFromOracle(sql_context ctx);
	int setSndAckData(sql_context ctx, vector<string>& vtSndAck);
	//int setReportData(sql_context ctx, vector<string>& vtReport, int telid);	// 2013.11.14 ahn
	int setReportData(sql_context ctx, vector<string>& vtReport);	// 2013.11.14 ahn
	//long long getSendData(sql_context ctx, char *sQID, char *sContPath, vector<string>& vtSend);
	int getSendData(sql_context ctx, int ipart, int tpart, int iline, vector<SNDSKY>& vtSend);
	int setWaitUpdate(sql_context ctx, char* status, SNDSKY& vtSend);
	int setTranDate(sql_context ctx, int mms_id);
	char* TrimR(char* szOrg, int leng);
	void log3(char *buf, int st, int err);
	char* trim(char* szOrg, int leng);

private:
	bool m_bThread;
	
	char tmpLog3[1024];
	/*inline string trimR(const string& str)
	{
		string::size_type n = str.find_last_not_of(" \t\v\n");
		return n == string::npos ? str : str.substr(0, n + 1);
	}*/
};

}

#endif /* DATABASEORA_H_ */
