#include "Encrypt.h"

void Encrypt::set_key()
{
	memset(iv, 0x00, sizeof(iv));
	strcpy((char*)ckey, "GodWithSejongLee");
}

void Encrypt::init_ctr(struct ctr_state *state, const unsigned char *iv)
{
	state->num = 0;
    memset(state->ecount, 0, AES_BLOCK_SIZE);
    memset(state->ivec+16 , 0, 16);
    memcpy(state->ivec, iv, 16);
}

// encrypt == decrypt(내용은 같다)
void Encrypt::encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read)
{       
	int i=0;
	int mod_len=0;

	AES_set_encrypt_key(ckey, KEY_SIZE, &ase_key);

	if( bytes_read < BYTES_SIZE){
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, bytes_read, &ase_key, state.ivec, state.ecount, &state.num);
		return;
	}
	// loop block size  = [ BYTES_SIZE ]
	for(i=BYTES_SIZE; i <= bytes_read ;i+=BYTES_SIZE){
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, BYTES_SIZE, &ase_key, state.ivec, state.ecount, &state.num);
		indata+=BYTES_SIZE;
		outdata+=BYTES_SIZE;
	}

	mod_len = bytes_read % BYTES_SIZE;
	if( mod_len != 0 ){
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, mod_len, &ase_key, state.ivec, state.ecount, &state.num);
	}
}

void Encrypt::decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read)
{       
	int i=0;
	int mod_len=0;

	AES_set_encrypt_key(ckey, KEY_SIZE, &ase_key);

	if( bytes_read < BYTES_SIZE)
	{
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, bytes_read, &ase_key, state.ivec, state.ecount, &state.num);
		return;
	}

	// loop block size  = [ BYTES_SIZE ]
	for(i=BYTES_SIZE; i <= bytes_read ;i+=BYTES_SIZE)
	{
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, BYTES_SIZE, &ase_key, state.ivec, state.ecount, &state.num);
		indata+=BYTES_SIZE;
		outdata+=BYTES_SIZE;
	}

	mod_len = bytes_read % BYTES_SIZE;
	if( mod_len != 0 )
	{
		struct ctr_state state;
		init_ctr(&state, iv);
		AES_ctr128_encrypt(indata, outdata, mod_len, &ase_key, state.ivec, state.ecount, &state.num);
	}

}

